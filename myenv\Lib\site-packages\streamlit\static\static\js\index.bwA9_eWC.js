import{s as H,r as a,aP as U,z as k,cu as S,C as B,j as n,br as j,bH as O,bs as _,b9 as K,bt as $,D as q,cv as w}from"./index.CbQtRkVt.js";import{u as A}from"./uniqueId.-ygIU7IL.js";import{u as G,a as N,b as J}from"./useOnInputChange.CDZx-L6q.js";import{a as M}from"./useBasicWidgetState.Bx3VaRHk.js";import{I as Q}from"./InputInstructions.D3IDU-eY.js";import{I as X}from"./input.D_45B0P-.js";import"./inputUtils.CptNuJwn.js";import"./FormClearHelper.Cdw5Y7_m.js";import"./base-input.DMlw5p7n.js";const Y=H("div",{target:"e1o1zy6o0"})("position:relative;");function Z({disabled:o,element:t,widgetMgr:s,fragmentId:u}){const[r,c]=a.useState(()=>W(s,t)??null),[T,y]=U(),[i,p]=a.useState(!1),x=a.useCallback(()=>{c(t.default??null),p(!0)},[t.default]),[V,m]=M({getStateFromWidgetMgr:W,getDefaultStateFromProto:tt,getCurrStateFromProto:et,updateWidgetMgrState:ot,element:t,widgetMgr:s,fragmentId:u,onFormCleared:x});G(V,r,c,i);const[C,g]=a.useState(!1),e=k(),[f]=a.useState(()=>A("text_input_")),{placeholder:F,formId:l,icon:d,maxChars:b}=t,h=a.useCallback(()=>{p(!1),m({value:r,fromUi:!0})},[r,m]),v=S({formId:l})?s.allowFormEnterToSubmit(l):i,z=C&&T>e.breakpoints.hideWidgetDetails,E=a.useCallback(()=>{i&&h(),g(!1)},[i,h]),P=a.useCallback(()=>{g(!0)},[]),R=N({formId:l,maxChars:b,setDirty:p,setUiValue:c,setValueWithSource:m}),L=J(l,h,i,s,u),I=d?.startsWith(":material"),D=I?"lg":"base";return B(Y,{className:"stTextInput","data-testid":"stTextInput",ref:y,children:[n($,{label:t.label,disabled:o,labelVisibility:j(t.labelVisibility?.value),htmlFor:f,children:t.help&&n(O,{children:n(_,{content:t.help,placement:K.TOP_RIGHT})})}),n(X,{value:r??"",placeholder:F,onBlur:E,onFocus:P,onChange:R,onKeyPress:L,"aria-label":t.label,disabled:o,id:f,type:at(t),autoComplete:t.autocomplete,startEnhancer:d&&n(q,{"data-testid":"stTextInputIcon",iconValue:d,size:D}),overrides:{Input:{style:{fontWeight:e.fontWeights.normal,minWidth:0,lineHeight:e.lineHeights.inputWidget,paddingRight:e.spacing.sm,paddingLeft:e.spacing.md,paddingBottom:e.spacing.sm,paddingTop:e.spacing.sm,"::placeholder":{color:e.colors.fadedText60}}},Root:{props:{"data-testid":"stTextInputRootElement"},style:{height:e.sizes.minElementHeight,borderLeftWidth:e.sizes.borderWidth,borderRightWidth:e.sizes.borderWidth,borderTopWidth:e.sizes.borderWidth,borderBottomWidth:e.sizes.borderWidth,paddingLeft:d?e.spacing.sm:0}},StartEnhancer:{style:{paddingLeft:0,paddingRight:0,minWidth:e.iconSizes.lg,color:I?e.colors.fadedText60:"inherit"}}}}),z&&n(Q,{dirty:i,value:r??"",maxLength:b,inForm:S({formId:l}),allowEnterToSubmit:v})]})}function W(o,t){return o.getStringValue(t)??null}function tt(o){return o.default??null}function et(o){return o.value??null}function ot(o,t,s,u){t.setStringValue(o,s.value,{fromUi:s.fromUi},u)}function at(o){return o.type===w.Type.PASSWORD?"password":"text"}const mt=a.memo(Z);export{mt as default};
