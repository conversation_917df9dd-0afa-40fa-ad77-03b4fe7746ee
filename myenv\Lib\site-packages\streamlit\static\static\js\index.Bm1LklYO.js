import{r as d,E as S,_ as x,s as c,H as O,C as p,j as a,bo as f,bp as F,b3 as _,b4 as w,bh as G,F as q,bF as K,aF as Y,bj as J,bG as z,N as h,b as m,bl as Q,bk as Z,br as ee,bH as te,bs as ie,b9 as se,bt as le}from"./index.CbQtRkVt.js";import{F as ne}from"./FormClearHelper.Cdw5Y7_m.js";import{g as b,F as y,b as ae,D as oe,I as re,C as de,a as ce,s as pe}from"./FileHelper.D0K06YBq.js";import{S as ge,P as ue}from"./ProgressBar.EhJ_lCOf.js";import{u as he}from"./Hooks.C_qx1sSw.js";import{U as v}from"./UploadFileInfo.C-jY39rj.js";var M=d.forwardRef(function(e,s){var t={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return d.createElement(S,x({iconAttrs:t,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:s}),d.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),d.createElement("path",{d:"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12l4.58-4.59z"}))});M.displayName="ChevronLeft";var V=d.forwardRef(function(e,s){var t={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return d.createElement(S,x({iconAttrs:t,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:s}),d.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),d.createElement("path",{d:"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6-6-6z"}))});V.displayName="ChevronRight";var B=d.forwardRef(function(e,s){var t={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return d.createElement(S,x({iconAttrs:t,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:s}),d.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),d.createElement("path",{d:"M19.35 10.04A7.49 7.49 0 0012 4C9.11 4 6.6 5.64 5.35 8.04A5.994 5.994 0 000 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM19 18H6c-2.21 0-4-1.79-4-4 0-2.05 1.53-3.76 3.56-3.97l1.07-.11.5-.95A5.469 5.469 0 0112 6c2.62 0 4.88 1.86 5.39 4.43l.3 1.5 1.53.11A2.98 2.98 0 0122 15c0 1.65-1.35 3-3 3zM8 13h2.55v3h2.9v-3H16l-4-4z"}))});B.displayName="CloudUpload";var D=d.forwardRef(function(e,s){var t={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return d.createElement(S,x({iconAttrs:t,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},e,{ref:s}),d.createElement("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"}))});D.displayName="Error";const L=c("section",{target:"e16xj5sw0"})(({isDisabled:e,theme:s})=>({display:"flex",alignItems:"center",padding:s.spacing.lg,backgroundColor:s.colors.secondaryBg,borderRadius:s.radii.default,border:s.colors.widgetBorderColor?`${s.sizes.borderWidth} solid ${s.colors.widgetBorderColor}`:void 0,":focus":{outline:"none"},":focus-visible":{boxShadow:`0 0 0 1px ${s.colors.primary}`},color:e?s.colors.gray:s.colors.bodyText})),P=c("div",{target:"e16xj5sw1"})({marginRight:"auto",alignItems:"center",display:"flex"}),E=c("span",{target:"e16xj5sw2"})(({theme:e})=>({color:e.colors.darkenedBgMix100,marginRight:e.spacing.lg})),fe=c("span",{target:"e16xj5sw3"})(({theme:e})=>({marginBottom:e.spacing.twoXS})),me=c("div",{target:"e16xj5sw4"})({display:"flex",flexDirection:"column"}),j=c("div",{target:"e16xj5sw5"})(({theme:e})=>({left:0,right:0,lineHeight:e.lineHeights.tight,paddingTop:e.spacing.md,paddingLeft:e.spacing.lg,paddingRight:e.spacing.lg})),Fe=c("ul",{target:"e16xj5sw6"})(({theme:e})=>({listStyleType:"none",margin:e.spacing.none,padding:e.spacing.none})),A=c("li",{target:"e16xj5sw7"})(({theme:e})=>({margin:e.spacing.none,padding:e.spacing.none})),H=c("div",{target:"e16xj5sw8"})(({theme:e})=>({display:"flex",alignItems:"baseline",flex:1,paddingLeft:e.spacing.lg,overflow:"hidden"})),N=c("div",{target:"e16xj5sw9"})(({theme:e})=>({marginRight:e.spacing.sm,marginBottom:e.spacing.twoXS,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"})),W=c("div",{target:"e16xj5sw10"})(({theme:e})=>({display:"flex",alignItems:"center",marginBottom:e.spacing.twoXS})),we=c("span",{target:"e16xj5sw11"})(({theme:e})=>({marginRight:e.spacing.twoXS})),ye=c("div",{target:"e16xj5sw12"})(({theme:e})=>({display:"flex",padding:e.spacing.twoXS,color:e.colors.darkenedBgMix100})),k=c("small",{target:"e16xj5sw13"})(({theme:e})=>({color:e.colors.red,fontSize:e.fontSizes.sm,height:e.fontSizes.sm,lineHeight:e.fontSizes.sm,display:"flex",alignItems:"center",whiteSpace:"nowrap"})),R=c("span",{target:"e16xj5sw14"})({}),Se=e=>({[L]:{display:"flex",flexDirection:"column",alignItems:"flex-start"},[P]:{marginBottom:e.spacing.lg},[E]:{display:"none"},[j]:{paddingRight:e.spacing.lg},[W]:{maxWidth:"inherit",flex:1,alignItems:"flex-start",marginBottom:e.spacing.sm},[N]:{width:e.sizes.full},[H]:{flexDirection:"column"},[k]:{height:"auto",whiteSpace:"initial"},[R]:{display:"none"},[A]:{margin:e.spacing.none,padding:e.spacing.none}}),xe=c("div",{target:"e16xj5sw15"})(({theme:e,width:s})=>{if(s<O("23rem"))return Se(e)}),I=c("small",{target:"ejh2rmr0"})(({kind:e,theme:s})=>{const{danger:t,fadedText60:i}=s.colors;return{color:e==="danger"?t:i,fontSize:s.fontSizes.sm,lineHeight:s.lineHeights.tight}}),Ue=({multiple:e,acceptedExtensions:s,maxSizeBytes:t})=>p(P,{"data-testid":"stFileUploaderDropzoneInstructions",children:[a(E,{children:a(f,{content:B,size:"threeXL"})}),p(me,{children:[p(fe,{children:["Drag and drop file",e?"s":""," here"]}),p(I,{children:[`Limit ${b(t,y.Byte,0)} per file`,s.length?` • ${s.map(i=>i.replace(/^\./,"").toUpperCase()).join(", ")}`:null]})]})]}),ve=d.memo(Ue),Ie=({onDrop:e,multiple:s,acceptedExtensions:t,maxSizeBytes:i,disabled:l,label:o})=>a(oe,{onDrop:e,multiple:s,accept:ae(t),maxSize:i,disabled:l,useFsAccessApi:!1,children:({getRootProps:n,getInputProps:r})=>p(L,{...n(),"data-testid":"stFileUploaderDropzone",isDisabled:l,"aria-label":o,children:[a("input",{"data-testid":"stFileUploaderDropzoneInput",...r()}),a(ve,{multiple:s,acceptedExtensions:t,maxSizeBytes:i}),a(F,{kind:w.SECONDARY,disabled:l,size:_.SMALL,children:"Browse files"})]})}),ze=d.memo(Ie),Ce=c("div",{target:"egc9vxm0"})(({theme:e})=>({display:"flex",alignItems:"center",justifyContent:"space-between",paddingBottom:e.spacing.twoXS,marginBottom:e.spacing.twoXS})),be=c("div",{target:"egc9vxm1"})(({theme:e})=>({display:"flex",alignItems:"center",justifyContent:"center",color:e.colors.fadedText40})),Me=({currentPage:e,totalPages:s,onNext:t,onPrevious:i})=>p(Ce,{"data-testid":"stFileUploaderPagination",children:[a(I,{children:`Showing page ${e} of ${s}`}),p(be,{children:[a(F,{onClick:i,kind:w.MINIMAL,children:a(f,{content:M,size:"xl"})}),a(F,{onClick:t,kind:w.MINIMAL,children:a(f,{content:V,size:"xl"})})]})]}),Ve=d.memo(Me),C=(e,s)=>Math.ceil(e.length/s),Be=e=>G(({pageSize:t,items:i,resetOnAdd:l,...o})=>{const[n,r]=d.useState(0),[g,U]=d.useState(()=>C(i,t)),u=he(i);d.useEffect(()=>{u&&u.length!==i.length&&U(C(i,t)),u&&u.length<i.length?l&&r(0):n+1>=g&&r(g-1)},[i,n,t,u,l,g]);const T=()=>{r(Math.min(n+1,g-1))},$=()=>{r(Math.max(0,n-1))},X=i.slice(n*t,n*t+t);return p(q,{children:[a(e,{items:X,...o}),i.length>t?a(Ve,{pageSize:t,totalPages:g,currentPage:n+1,onNext:T,onPrevious:$}):null]})},e),De=({fileInfo:e})=>e.status.type==="uploading"?a(ue,{value:e.status.progress,size:ge.SMALL}):e.status.type==="error"?p(k,{children:[a(we,{"data-testid":"stFileUploaderFileErrorMessage",children:e.status.errorMessage}),a(R,{children:a(f,{content:D,size:"lg"})})]}):e.status.type==="uploaded"?a(I,{children:b(e.size,y.Byte)}):null,Le=({fileInfo:e,onDelete:s})=>p(W,{className:"stFileUploaderFile","data-testid":"stFileUploaderFile",children:[a(ye,{children:a(f,{content:re,size:"twoXL"})}),p(H,{className:"stFileUploaderFileData",children:[a(N,{className:"stFileUploaderFileName","data-testid":"stFileUploaderFileName",title:e.name,children:e.name}),a(De,{fileInfo:e})]}),a("div",{"data-testid":"stFileUploaderDeleteBtn",children:a(F,{onClick:()=>s(e.id),kind:w.MINIMAL,children:a(f,{content:de,size:"lg"})})})]}),Pe=d.memo(Le),Ee=({items:e,onDelete:s})=>a(Fe,{children:e.map(t=>a(A,{children:a(Pe,{fileInfo:t,onDelete:s})},t.id))}),je=Be(Ee),Ae=e=>a(j,{children:a(je,{...e})}),He=d.memo(Ae);class Ne extends d.PureComponent{constructor(s){super(s),this.formClearHelper=new ne,this.localFileIdCounter=1,this.forceUpdatingStatus=!1,this.componentDidUpdate=()=>{if(this.status!=="ready")return;const t=this.createWidgetValue(),{element:i,widgetMgr:l,fragmentId:o}=this.props,n=l.getFileUploaderStateValue(i);Y(t,n)||l.setFileUploaderStateValue(i,t,{fromUi:!0},o)},this.dropHandler=(t,i)=>{const{element:l}=this.props,{multipleFiles:o}=l;if(!o&&t.length===0&&i.length>1){const n=i.findIndex(r=>r.errors.length===1&&r.errors[0].code==="too-many-files");n>=0&&(t.push(i[n].file),i.splice(n,1))}if(this.props.uploadClient.fetchFileURLs(t).then(n=>{if(!o&&t.length>0){const r=this.state.files.find(g=>g.status.type!=="error");r&&(this.forceUpdatingStatus=!0,this.deleteFile(r.id),this.forceUpdatingStatus=!1)}J(n,t).forEach(([r,g])=>{this.uploadFile(r,g)})}).catch(n=>{this.addFiles(t.map(r=>new v(r.name,r.size,this.nextLocalFileId(),{type:"error",errorMessage:n})))}),i.length>0){const n=i.map(r=>ce(r,this.nextLocalFileId(),this.maxUploadSizeInBytes));this.addFiles(n)}},this.uploadFile=(t,i)=>{const l=z.CancelToken.source(),o=new v(i.name,i.size,this.nextLocalFileId(),{type:"uploading",cancelToken:l,progress:1});this.addFile(o),this.props.uploadClient.uploadFile(this.props.element,t.uploadUrl,i,n=>this.onUploadProgress(n,o.id),l.token).then(()=>this.onUploadComplete(o.id,t)).catch(n=>{z.isCancel(n)||this.updateFile(o.id,o.setStatus({type:"error",errorMessage:n?n.toString():"Unknown error"}))})},this.onUploadComplete=(t,i)=>{const l=this.getFile(t);h(l)||l.status.type!=="uploading"||this.updateFile(l.id,l.setStatus({type:"uploaded",fileId:i.fileId,fileUrls:i}))},this.deleteFile=t=>{const i=this.getFile(t);h(i)||(i.status.type==="uploading"&&i.status.cancelToken.cancel(),i.status.type==="uploaded"&&i.status.fileUrls.deleteUrl&&this.props.uploadClient.deleteFile(i.status.fileUrls.deleteUrl),this.removeFile(t))},this.addFile=t=>{m.flushSync(()=>{this.setState(i=>({files:[...i.files,t]}))})},this.addFiles=t=>{m.flushSync(()=>{this.setState(i=>({files:[...i.files,...t]}))})},this.removeFile=t=>{m.flushSync(()=>{this.setState(i=>({files:i.files.filter(l=>l.id!==t)}))})},this.getFile=t=>this.state.files.find(i=>i.id===t),this.updateFile=(t,i)=>{m.flushSync(()=>{this.setState(l=>({files:l.files.map(o=>o.id===t?i:o)}))})},this.onUploadProgress=(t,i)=>{const l=this.getFile(i);if(h(l)||l.status.type!=="uploading")return;const o=Math.round(t.loaded*100/t.total);l.status.progress!==o&&this.updateFile(i,l.setStatus({type:"uploading",cancelToken:l.status.cancelToken,progress:o}))},this.onFormCleared=()=>{m.flushSync(()=>{this.setState({files:[]},()=>{const t=this.createWidgetValue();if(h(t))return;const{widgetMgr:i,element:l,fragmentId:o}=this.props;i.setFileUploaderStateValue(l,t,{fromUi:!0},o)})})},this.state=this.initialValue}get initialValue(){const s={files:[]},{widgetMgr:t,element:i}=this.props,l=t.getFileUploaderStateValue(i);if(h(l))return s;const{uploadedFileInfo:o}=l;return h(o)||o.length===0?s:{files:o.map(n=>{const r=n.name,g=n.size,U=n.fileId,u=n.fileUrls;return new v(r,g,this.nextLocalFileId(),{type:"uploaded",fileId:U,fileUrls:u})})}}componentWillUnmount(){this.formClearHelper.disconnect()}get maxUploadSizeInBytes(){const s=this.props.element.maxUploadSizeMb;return pe(s,y.Megabyte,y.Byte)}get status(){const s=t=>t.status.type==="uploading";return this.state.files.some(s)||this.forceUpdatingStatus?"updating":"ready"}componentDidMount(){const s=this.createWidgetValue(),{element:t,widgetMgr:i,fragmentId:l}=this.props;i.getFileUploaderStateValue(t)===void 0&&i.setFileUploaderStateValue(t,s,{fromUi:!1},l)}createWidgetValue(){const s=this.state.files.filter(t=>t.status.type==="uploaded").map(t=>{const{name:i,size:l,status:o}=t,{fileId:n,fileUrls:r}=o;return new Q({fileId:n,fileUrls:r,name:i,size:l})});return new Z({uploadedFileInfo:s})}render(){const{files:s}=this.state,{element:t,disabled:i,widgetMgr:l,width:o}=this.props,n=t.type;this.formClearHelper.manageFormClearListener(l,t.formId,this.onFormCleared);const r=s.slice().reverse();return p(xe,{className:"stFileUploader","data-testid":"stFileUploader",width:o,children:[a(le,{label:t.label,disabled:i,labelVisibility:ee(t.labelVisibility?.value),children:t.help&&a(te,{children:a(ie,{content:t.help,placement:se.TOP_RIGHT})})}),a(ze,{onDrop:this.dropHandler,multiple:t.multipleFiles,acceptedExtensions:n,maxSizeBytes:this.maxUploadSizeInBytes,label:t.label,disabled:i}),r.length>0&&a(He,{items:r,pageSize:3,onDelete:this.deleteFile,resetOnAdd:!0})]})}nextLocalFileId(){return this.localFileIdCounter++}}const Oe=K(d.memo(Ne));export{Oe as default};
