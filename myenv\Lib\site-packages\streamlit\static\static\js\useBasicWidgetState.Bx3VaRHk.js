import{r as e,N as C}from"./index.CbQtRkVt.js";import{u as h}from"./FormClearHelper.Cdw5Y7_m.js";function p({getStateFromWidgetMgr:i,getDefaultState:r,updateWidgetMgrState:l,element:s,widgetMgr:u,fragmentId:o,onFormCleared:f}){const[n,t]=e.useState(()=>i(u,s)??r(u,s)),[c,a]=e.useState({value:n,fromUi:!1});e.useEffect(()=>{C(c)||(a(null),t(c.value),l(s,u,c,o))},[c,l,s,u,o]);const V=e.useCallback(()=>{a({value:r(u,s),fromUi:!0}),f?.()},[a,s,r,u,f]);return h({widgetMgr:u,element:s,onFormCleared:V}),[n,a]}function U({getStateFromWidgetMgr:i,getDefaultStateFromProto:r,getCurrStateFromProto:l,updateWidgetMgrState:s,element:u,widgetMgr:o,fragmentId:f,onFormCleared:n}){const t=e.useCallback((V,x)=>r(x),[r]),[c,a]=p({getStateFromWidgetMgr:i,getDefaultState:t,updateWidgetMgrState:s,element:u,widgetMgr:o,fragmentId:f,onFormCleared:n});return e.useEffect(()=>{u.setValue&&(u.setValue=!1,a({value:l(u),fromUi:!1}))},[u,l,a]),[c,a]}export{U as a,p as u};
