import{s as T,r as a,L as b,bi as L,j as p,l as N}from"./index.CbQtRkVt.js";const U=T("iframe",{target:"eymnmwl0"})(({theme:r})=>({colorScheme:"normal",border:"none",padding:r.spacing.none,margin:r.spacing.none,width:"100%",aspectRatio:"16 / 9"})),R=<PERSON>.getLogger("Video"),O={width:"100%"};function A({element:r,endpoints:s,elementMgr:l}){const{libConfig:y}=a.useContext(b),n=a.useRef(null),{type:v,url:f,startTime:i,subtitles:d,endTime:o,loop:c,autoplay:m,muted:E}=r,S=a.useMemo(()=>{if(!r.id)return!0;const e=l.getElementState(r.id,"preventAutoplay");return e||l.setElementState(r.id,"preventAutoplay",!0),e??!1},[r.id,l]),g=a.useMemo(()=>JSON.stringify(d?d.map(e=>s.buildMediaURL(`${e.url}`)):[]),[d,s]);a.useEffect(()=>{const e=JSON.parse(g);e.length!==0&&e.forEach(t=>{s.checkSourceUrlResponse(t,"Video Subtitle")})},[g,s]),a.useEffect(()=>{n.current&&(n.current.currentTime=i)},[i]),a.useEffect(()=>{const e=n.current,t=()=>{e&&(e.currentTime=r.startTime)};return e&&e.addEventListener("loadedmetadata",t),()=>{e&&e.removeEventListener("loadedmetadata",t)}},[r]),a.useEffect(()=>{const e=n.current;if(!e)return;let t=!1;const u=()=>{o>0&&e.currentTime>=o&&(c?(e.currentTime=i||0,e.play()):t||(t=!0,e.pause()))};return o>0&&e.addEventListener("timeupdate",u),()=>{e&&o>0&&e.removeEventListener("timeupdate",u)}},[o,c,i]),a.useEffect(()=>{const e=n.current;if(!e)return;const t=()=>{c&&(e.currentTime=i||0,e.play())};return e.addEventListener("ended",t),()=>{e&&e.removeEventListener("ended",t)}},[c,i]);const V=e=>{const t=new URL(e);if(i&&!isNaN(i)&&t.searchParams.append("start",i.toString()),o&&!isNaN(o)&&t.searchParams.append("end",o.toString()),c){t.searchParams.append("loop","1");const u=t.pathname.split("/").pop();u&&t.searchParams.append("playlist",u)}return m&&t.searchParams.append("autoplay","1"),E&&t.searchParams.append("mute","1"),t.toString()};if(v===L.Type.YOUTUBE_IFRAME)return p(U,{className:"stVideo","data-testid":"stVideo",title:f,src:V(f),allow:"autoplay; encrypted-media",allowFullScreen:!0});const h=e=>{const t=e.currentTarget.src;R.error(`Client Error: Video source error - ${t}`),s.sendClientErrorToHost("Video","Video source failed to load","onerror triggered",t)};return p("video",{className:"stVideo","data-testid":"stVideo",ref:n,controls:!0,muted:E,autoPlay:m&&!S,src:s.buildMediaURL(f),style:O,crossOrigin:y.resourceCrossOriginMode,onError:h,children:d&&d.map((e,t)=>p("track",{kind:"captions",src:s.buildMediaURL(`${e.url}`),label:`${e.label}`,default:t===0,"data-testid":"stVideoSubtitle"},t))})}const w=a.memo(A);export{w as default};
